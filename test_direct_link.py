#!/usr/bin/env python3
"""测试Cloudreve直链功能"""

import sys
import time
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_direct_link():
    """测试Cloudreve直链功能"""
    print("🔍 测试Cloudreve直链功能")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 创建唯一的测试文件名（使用时间戳）
        timestamp = int(time.time())
        test_file = Path(f"test_direct_link_{timestamp}.txt")
        test_content = f"""这是测试Cloudreve直链功能的文件
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
时间戳: {timestamp}
文件内容: 这个文件将用于测试直链功能，确保可以通过直链URL直接访问文件内容。
"""
        
        try:
            # 1. 上传测试文件
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            print(f"\n📁 创建测试文件:")
            print(f"   文件名: {test_file.name}")
            print(f"   文件大小: {len(test_content.encode('utf-8'))} 字节")
            
            print(f"\n🚀 上传文件...")
            
            def progress_callback(uploaded, total):
                percent = (uploaded / total) * 100
                print(f"   📊 上传进度: {percent:.1f}% ({uploaded}/{total} 字节)")
            
            # 上传文件
            result = client.upload_file(
                str(test_file), 
                progress_callback=progress_callback
            )
            
            print(f"✅ 文件上传完成!")
            print(f"📁 文件URI: {result}")
            
            file_uri = result  # upload_file返回的是文件URI
            
            # 2. 测试创建单个直链
            print(f"\n🔗 测试创建单个直链...")
            try:
                direct_link = client.create_direct_link(file_uri)
                print(f"✅ 单个直链创建成功:")
                print(f"   🌐 直链URL: {direct_link}")
                
                # 3. 测试直链是否可访问
                print(f"\n🌐 测试直链访问...")
                try:
                    response = requests.get(direct_link, timeout=10)
                    print(f"   HTTP状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        content_received = response.text
                        print(f"   ✅ 直链访问成功!")
                        print(f"   📄 接收到的内容长度: {len(content_received)} 字符")
                        
                        # 验证内容是否正确
                        if test_content.strip() == content_received.strip():
                            print(f"   ✅ 文件内容验证成功!")
                        else:
                            print(f"   ⚠️ 文件内容不匹配")
                            print(f"   原始内容前100字符: {test_content[:100]}")
                            print(f"   接收内容前100字符: {content_received[:100]}")
                    else:
                        print(f"   ❌ 直链访问失败: HTTP {response.status_code}")
                        print(f"   响应内容: {response.text[:200]}")
                        
                except requests.RequestException as e:
                    print(f"   ❌ 直链访问异常: {e}")
                
            except CloudreveError as e:
                print(f"❌ 创建单个直链失败: {e}")
                if e.response:
                    print(f"   详细错误: {e.response}")
            
            # 4. 测试批量创建直链
            print(f"\n🔗 测试批量创建直链...")
            try:
                direct_links = client.create_direct_links([file_uri])
                print(f"✅ 批量直链创建成功:")
                
                for i, link_info in enumerate(direct_links, 1):
                    print(f"   {i}. 文件URI: {link_info.get('file_url')}")
                    print(f"      直链URL: {link_info.get('link')}")
                
            except CloudreveError as e:
                print(f"❌ 批量创建直链失败: {e}")
                if e.response:
                    print(f"   详细错误: {e.response}")
            
            # 5. 验证文件在服务器上的存在
            print(f"\n📋 验证文件在服务器上的存在...")
            try:
                # 查询根目录文件列表
                params = {
                    'uri': 'cloudreve://my/',
                    'page': 0,
                    'page_size': 50
                }
                response = client._make_request('GET', '/api/v4/file', params=params)
                
                if 'data' in response and 'files' in response['data']:
                    files = response['data']['files']
                    uploaded_file = None
                    
                    for file_info in files:
                        if file_info.get('name') == test_file.name:
                            uploaded_file = file_info
                            break
                    
                    if uploaded_file:
                        print(f"✅ 文件验证成功:")
                        print(f"   文件名: {uploaded_file.get('name')}")
                        print(f"   文件ID: {uploaded_file.get('id')}")
                        print(f"   文件大小: {uploaded_file.get('size')} 字节")
                        print(f"   创建时间: {uploaded_file.get('created_at')}")
                    else:
                        print(f"⚠️ 未在文件列表中找到上传的文件")
                
            except CloudreveError as e:
                print(f"⚠️ 验证文件存在失败: {e}")
            
        except CloudreveError as e:
            print(f"❌ 文件上传失败: {e}")
            if e.response:
                print(f"   详细错误: {e.response}")
        
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        finally:
            # 清理本地测试文件
            if test_file.exists():
                test_file.unlink()
                print(f"\n🗑️ 清理本地测试文件: {test_file.name}")
        
        # 登出
        client.logout()
        print(f"\n✅ 测试完成")
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")
        if e.response:
            print(f"   详细错误: {e.response}")


if __name__ == "__main__":
    test_direct_link()
