#!/usr/bin/env python3
"""测试API端点"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_api_endpoints():
    """测试各种API端点"""
    print("🔍 测试Cloudreve API端点")
    print("=" * 50)
    
    # 直接从项目根目录的config.toml读取配置
    try:
        # 手动解析TOML配置文件
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单解析Cloudreve配置
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                # 移除行内注释
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 测试各种可能的API端点
        test_endpoints = [
            # 用户相关
            ('/api/v4/user', 'GET', '获取用户信息'),
            ('/api/v4/user/storage', 'GET', '获取存储信息'),
            ('/api/v4/user/setting/policies', 'GET', '获取存储策略'),
            
            # 可能的替代端点
            ('/api/v4/user/policies', 'GET', '获取用户策略'),
            ('/api/v4/policies', 'GET', '获取策略列表'),
            ('/api/v4/storage/policies', 'GET', '获取存储策略列表'),
            
            # 文件相关
            ('/api/v4/file/list', 'GET', '列出文件'),
            
            # 其他可能的端点
            ('/api/v4/user/setting', 'GET', '获取用户设置'),
            ('/api/v4/user/preference', 'GET', '获取用户偏好'),
        ]
        
        for endpoint, method, description in test_endpoints:
            print(f"\n🔍 测试: {description}")
            print(f"   端点: {method} {endpoint}")
            
            try:
                if method == 'GET':
                    response = client._make_request('GET', endpoint)
                else:
                    response = client._make_request(method, endpoint)
                
                print(f"   ✅ 成功! 响应: {response}")
                
                # 如果是存储策略相关的端点，显示详细信息
                if 'policies' in endpoint.lower() and 'data' in response:
                    data = response['data']
                    if isinstance(data, list):
                        print(f"   📋 找到 {len(data)} 个策略:")
                        for i, item in enumerate(data[:3], 1):  # 只显示前3个
                            if isinstance(item, dict):
                                item_id = item.get('id', 'N/A')
                                item_name = item.get('name', 'N/A')
                                item_type = item.get('type', 'N/A')
                                print(f"      {i}. ID: {item_id}, 名称: {item_name}, 类型: {item_type}")
                
            except CloudreveError as e:
                print(f"   ❌ 失败: {e}")
                if e.code:
                    print(f"   错误码: {e.code}")
        
        # 测试不带/api/v4前缀的端点
        print(f"\n" + "="*50)
        print(f"🔍 测试不带/api/v4前缀的端点")
        
        no_prefix_endpoints = [
            ('/user/setting/policies', 'GET', '获取存储策略(无前缀)'),
            ('/user/storage', 'GET', '获取存储信息(无前缀)'),
            ('/user', 'GET', '获取用户信息(无前缀)'),
        ]
        
        for endpoint, method, description in no_prefix_endpoints:
            print(f"\n🔍 测试: {description}")
            print(f"   端点: {method} {endpoint}")
            
            try:
                response = client._make_request('GET', endpoint)
                print(f"   ✅ 成功! 响应: {response}")
                
            except CloudreveError as e:
                print(f"   ❌ 失败: {e}")
        
        # 登出
        client.logout()
        print(f"\n✅ 测试完成")
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_api_endpoints()
