# VideoReader - 智能视频阅读器

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Development-yellow.svg)]()

## 📖 项目简介

VideoReader是一个智能视频阅读器，旨在解决视频信息密度低、观看效率不高的问题。通过将视频转换为以文本、图片为主，视频为辅的阅读方式，大幅提升学习和信息获取效率。

### ✨ 核心特性

- 🎯 **智能分段**: 支持多种解析方式（画面变化、文本长度、时间固定、静音检测）
- 🗣️ **语音识别**: 高精度语音转文字，支持多种引擎（Whisper、Azure、阿里云Paraformer）
- 📱 **直观界面**: 左侧段落列表，右侧图文显示，操作简单直观
- ⚡ **快速定位**: 点击段落即可跳转到对应时间点
- 💾 **元数据保存**: 解析结果保存为JSON，支持快速重新加载
- 🎮 **播放控制**: 完整的视频播放控制功能
- 📤 **多格式导出**: 支持文本、字幕、截图等多种格式导出

### 🎯 适用场景

- **在线课程学习**: 快速浏览课程要点，定位重要知识点
- **会议录音整理**: 将会议视频转换为可搜索的文本记录
- **视频内容分析**: 对视频内容进行结构化分析和整理
- **学术研究**: 从视频资料中提取关键信息

## 🏗️ 项目架构

```
VideoReader/
├── src/                    # 源代码目录
│   ├── core/              # 核心抽象层
│   │   ├── base_parser.py # 解析器基类
│   │   ├── video_processor.py # 视频处理核心
│   │   └── audio_processor.py # 音频处理核心
│   ├── business/          # 业务逻辑层
│   │   ├── parsers/       # 具体解析器实现
│   │   ├── speech_recognition.py # 语音识别
│   │   ├── metadata_manager.py   # 元数据管理
│   │   └── export_manager.py     # 导出功能
│   ├── ui/               # 用户界面层
│   │   ├── components/   # 界面组件
│   │   ├── main_window.py # 主窗口
│   │   └── styles/       # 样式文件
│   ├── utils/            # 工具和辅助功能
│   ├── main.py           # 程序入口
│   ├── app.py            # 可视化界面入口
│   ├── cli.py            # 命令行入口
│   └── function_interface.py # 功能接口层
├── tests/                # 测试文件
├── doc/                  # 项目文档
├── requirements.txt      # 依赖列表
└── README.md
```

## 🚀 快速开始

### 环境要求

- Python 3.8 或更高版本
- FFmpeg（用于音视频处理）
- 至少4GB内存
- 至少2GB可用磁盘空间

### 快速安装（推荐）

```bash
# 1. 克隆项目
git clone https://github.com/your-username/VideoReader.git
cd VideoReader

# 2. 运行自动安装脚本
python scripts/install.py

# 3. 验证安装
python scripts/verify_project.py
```

### 手动安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/VideoReader.git
cd VideoReader
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **安装FFmpeg**
- Windows: 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载并添加到PATH
- macOS: `brew install ffmpeg`
- Ubuntu: `sudo apt install ffmpeg`

5. **配置应用（推荐使用TOML配置文件）**

#### 方法1：使用配置向导（推荐）
```bash
python scripts/create_config.py
```

#### 方法2：手动创建配置文件
创建 `~/.videoreader/config.toml` 文件：
```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
```

#### 方法3：使用环境变量（备选）
```bash
# 阿里云API密钥
export DASHSCOPE_API_KEY='your-dashscope-api-key'

# Cloudreve文件存储（用于Paraformer）
export CLOUDREVE_BASE_URL='http://your-cloudreve-server.com:5212'
export CLOUDREVE_USERNAME='your-username'
export CLOUDREVE_PASSWORD='your-password'
```

6. **运行程序**
```bash
# 启动图形界面
python src/main.py

# 或使用命令行模式
python src/main.py --cli

# 查看帮助
python src/main.py --help
```

## 📚 使用指南

### 基本使用流程

1. **加载视频**: 点击"打开视频"按钮或拖拽视频文件到界面
2. **选择解析方式**: 根据视频类型选择合适的解析器
3. **开始解析**: 点击"开始解析"，等待语音识别和分段完成
4. **浏览内容**: 在左侧段落列表中浏览，点击段落查看详细内容
5. **播放控制**: 使用右侧控制面板播放、暂停、调整播放速度
6. **导出结果**: 根据需要导出文本、字幕或截图

### 解析器说明

| 解析器类型 | 适用场景 | 特点 |
|-----------|----------|------|
| 画面变化分段 | 学习视频、演示视频 | 根据画面变化程度自动分段 |
| 文本长度分段 | 讲座视频、访谈视频 | 按文本字数均匀分段 |
| 时间固定分段 | 新闻视频、短视频 | 按固定时间间隔分段 |
| 静音分段 | 会议录音、课程视频 | 根据静音间隔分段 |

## 🛠️ 开发指南

### 开发环境搭建

详细的开发环境搭建和开发规范请参考：
- [需求文档](需求文档.md)
- [架构设计](doc/架构设计.md)
- [开发指南](doc/开发指南.md)

### 贡献代码

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 遵循 PEP 8 Python 代码规范
- 使用类型提示（Type Hints）
- 函数和类必须有文档字符串
- 提交前运行测试确保代码质量

## 📋 开发计划

- [x] 项目需求分析和架构设计
- [ ] 核心抽象层开发（BaseParser、VideoProcessor、AudioProcessor）
- [ ] 语音识别功能实现
- [ ] 画面变化分段解析器实现
- [ ] 命令行界面开发
- [ ] 图形用户界面开发
- [ ] 其他解析器实现（文本长度、时间固定、静音分段）
- [ ] 元数据管理和导出功能
- [ ] 测试和性能优化
- [ ] 文档完善和发布

## 🤝 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 查看 [常见问题](doc/开发指南.md#7-常见问题解决)
- 提交 [Issue](https://github.com/your-username/VideoReader/issues)
- 发送邮件至 <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目和服务的支持：
- [OpenAI Whisper](https://github.com/openai/whisper) - 语音识别
- [阿里云Paraformer](https://help.aliyun.com/zh/dashscope/developer-reference/paraformer-api) - 语音识别
- [Cloudreve](https://github.com/cloudreve/Cloudreve) - 文件存储服务
- [FFmpeg](https://ffmpeg.org/) - 音视频处理
- [OpenCV](https://opencv.org/) - 计算机视觉
- [PyQt](https://www.riverbankcomputing.com/software/pyqt/) - 图形界面框架

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！



http://150.158.84.19:32203/s/3BHn