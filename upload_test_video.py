#!/usr/bin/env python3
"""上传test.mp4到Cloudreve进行测试"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def upload_test_video():
    """上传test.mp4文件到Cloudreve"""
    print("🎬 上传test.mp4到Cloudreve测试")
    print("=" * 60)
    
    # 检查test.mp4文件
    test_file = Path("test.mp4")
    if not test_file.exists():
        print("❌ test.mp4文件不存在")
        return False
    
    file_size = test_file.stat().st_size
    print(f"📁 文件信息:")
    print(f"   文件名: {test_file.name}")
    print(f"   文件大小: {format_size(file_size)}")
    print(f"   文件路径: {test_file.absolute()}")
    
    # 直接从项目根目录的config.toml读取配置
    try:
        # 手动解析TOML配置文件
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()

        # 简单解析Cloudreve配置
        base_url = ''
        username = ''
        password = ''

        lines = content.split('\n')
        in_cloudreve_section = False

        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue

            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")

                # 移除行内注释
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")

                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value

        print(f"📋 从config.toml读取到的配置:")
        print(f"   base_url: {base_url}")
        print(f"   username: {username}")
        print(f"   password: {'*' * len(password) if password else '(空)'}")

    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    if not all([base_url, username, password]):
        print("❌ 配置信息不完整，请检查config.toml文件")
        return False
    
    if '@' not in username:
        print(f"❌ 用户名不是email格式: {username}")
        print("请运行 python fix_cloudreve_config.py 修正配置")
        return False
    
    print(f"\n🔗 连接信息:")
    print(f"   服务器: {base_url}")
    print(f"   用户: {username}")
    
    try:
        # 创建客户端
        client = CloudreveClient(base_url, username, password)
        
        # 测试连接
        print(f"\n📡 测试服务器连接...")
        version = client.ping()
        print(f"   ✅ 服务器版本: {version}")
        
        # 登录
        print(f"\n🔐 正在登录...")
        try:
            login_result = client.login()
            user_info = login_result['user']
            print(f"   ✅ 登录成功: {user_info['nickname']} ({user_info['email']})")
            
            # 显示用户组信息
            group_info = user_info.get('group', {})
            print(f"   用户组: {group_info.get('name', 'N/A')}")
            
        except CloudreveError as e:
            if e.code == 40003:  # 需要验证码
                print("   ⚠️ 服务器要求验证码")
                print("   正在获取验证码...")
                
                try:
                    captcha_image, ticket = client.get_captcha()
                    print(f"   验证码ticket: {ticket}")
                    
                    # 这里需要用户手动输入验证码
                    print("\n   💡 请在浏览器中访问Cloudreve查看验证码图片")
                    print(f"   或者查看验证码base64数据: {captcha_image[:50]}...")
                    
                    captcha_input = input("   请输入验证码: ").strip()
                    
                    if captcha_input:
                        login_result = client.login(captcha=captcha_input, ticket=ticket)
                        user_info = login_result['user']
                        print(f"   ✅ 登录成功: {user_info['nickname']} ({user_info['email']})")
                    else:
                        print("   ❌ 验证码不能为空")
                        return False
                        
                except CloudreveError as e2:
                    print(f"   ❌ 获取验证码失败: {e2}")
                    return False
            else:
                print(f"   ❌ 登录失败: {e}")
                return False
        
        # 获取存储信息
        print(f"\n💾 存储空间信息:")
        try:
            storage_info = client.get_storage_info()
            used = storage_info.get('used', 0)
            total = storage_info.get('total', 0)
            
            print(f"   已使用: {format_size(used)}")
            print(f"   总容量: {format_size(total)}")
            
            if total > 0:
                usage_percent = (used / total) * 100
                print(f"   使用率: {usage_percent:.1f}%")
                
                # 检查是否有足够空间
                if (used + file_size) > total:
                    print(f"   ⚠️ 存储空间不足，需要 {format_size(file_size)}，剩余 {format_size(total - used)}")
                    
        except CloudreveError as e:
            print(f"   ⚠️ 获取存储信息失败: {e}")
        
        # 上传文件
        print(f"\n📤 开始上传文件...")
        
        # 进度回调函数
        last_progress_time = time.time()
        def progress_callback(uploaded, total):
            nonlocal last_progress_time
            current_time = time.time()
            
            # 每秒更新一次进度
            if current_time - last_progress_time >= 1.0 or uploaded == total:
                percent = (uploaded / total) * 100
                uploaded_mb = uploaded / (1024 * 1024)
                total_mb = total / (1024 * 1024)
                
                print(f"   📊 上传进度: {percent:.1f}% ({uploaded_mb:.1f}MB / {total_mb:.1f}MB)")
                last_progress_time = current_time
        
        start_time = time.time()
        
        try:
            result = client.upload_file(
                file_path=str(test_file),
                progress_callback=progress_callback
            )
            
            end_time = time.time()
            upload_time = end_time - start_time
            upload_speed = file_size / upload_time / (1024 * 1024)  # MB/s
            
            print(f"   ✅ 上传完成!")
            print(f"   上传时间: {upload_time:.1f}秒")
            print(f"   平均速度: {upload_speed:.1f}MB/s")
            print(f"   文件URI: {result}")
            
        except CloudreveError as e:
            print(f"   ❌ 上传失败: {e}")
            client.logout()
            return False
        
        # 验证上传结果 - 搜索刚上传的文件
        print(f"\n🔍 验证上传结果...")
        try:
            search_results = client.search_files("test.mp4")
            found_files = search_results.get('items', [])
            
            if found_files:
                print(f"   ✅ 找到 {len(found_files)} 个匹配文件:")
                
                for i, file_info in enumerate(found_files[:3], 1):  # 显示前3个
                    name = file_info.get('name', 'Unknown')
                    size = format_size(file_info.get('size', 0))
                    date = file_info.get('date', 'Unknown')
                    file_id = file_info.get('id', 'N/A')
                    
                    print(f"   {i}. {name} ({size}) - {date}")
                    
                    # 如果找到完全匹配的文件，创建分享链接
                    if name == "test.mp4":
                        print(f"\n🔗 为test.mp4创建分享链接...")
                        try:
                            share_info = client.create_share_link(
                                file_ids=[file_id],
                                expire_days=7,  # 7天有效期
                                preview_enabled=True
                            )
                            
                            share_key = share_info.get('key', '')
                            share_url = f"{base_url}/s/{share_key}"
                            
                            print(f"   ✅ 分享链接创建成功:")
                            print(f"   🌐 分享链接: {share_url}")
                            print(f"   ⏰ 有效期: 7天")
                            print(f"   👁️ 允许预览: 是")
                            
                        except CloudreveError as e:
                            print(f"   ⚠️ 创建分享链接失败: {e}")
                        
                        break
            else:
                print(f"   ⚠️ 未找到上传的文件，可能需要等待索引更新")
                
        except CloudreveError as e:
            print(f"   ⚠️ 搜索文件失败: {e}")
        
        # 登出
        print(f"\n🚪 正在登出...")
        client.logout()
        print("   ✅ 登出成功")
        
        print(f"\n🎉 test.mp4上传测试完成!")
        return True
        
    except CloudreveError as e:
        print(f"\n❌ 操作失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        return False


def main():
    """主函数"""
    success = upload_test_video()
    
    if success:
        print("\n✅ 测试成功完成!")
        print("💡 提示: 您可以在Cloudreve网页界面中查看上传的文件")
    else:
        print("\n❌ 测试失败")
        print("💡 提示: 请检查网络连接和认证信息")


if __name__ == "__main__":
    main()
