#!/usr/bin/env python3
"""探索Cloudreve上传API"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def explore_upload_apis():
    """探索可用的上传API"""
    print("🔍 探索Cloudreve上传API")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 测试各种可能的上传相关API端点
        print(f"\n📤 测试上传相关API端点...")
        
        upload_endpoints = [
            '/api/v4/file/upload',
            '/api/v4/upload',
            '/api/v4/file/create',
            '/api/v4/file/put',
            '/api/v4/files/upload'
        ]
        
        # 测试不同的上传数据格式
        test_data_formats = [
            # 格式1: 基本格式
            {
                'uri': 'cloudreve://my/test.mp4',
                'size': 493680,
                'policy_id': '1'
            },
            # 格式2: 不指定策略ID
            {
                'uri': 'cloudreve://my/test.mp4',
                'size': 493680
            },
            # 格式3: 简化URI
            {
                'uri': '/test.mp4',
                'size': 493680,
                'policy_id': '1'
            },
            # 格式4: 文件名格式
            {
                'filename': 'test.mp4',
                'size': 493680,
                'policy_id': '1'
            },
            # 格式5: 路径格式
            {
                'path': '/test.mp4',
                'size': 493680,
                'policy_id': '1'
            }
        ]
        
        for endpoint in upload_endpoints:
            print(f"\n🔍 测试端点: {endpoint}")
            
            for i, test_data in enumerate(test_data_formats, 1):
                print(f"   格式{i}: {test_data}")
                
                try:
                    response = client._make_request('PUT', endpoint, json=test_data)
                    print(f"   ✅ 成功! 响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
                    
                    # 如果成功创建了会话，尝试删除它
                    if 'data' in response and 'session_id' in response['data']:
                        session_id = response['data']['session_id']
                        try:
                            client._make_request('DELETE', f'/api/v4/file/upload/{session_id}')
                            print(f"   🗑️ 已清理测试会话: {session_id}")
                        except:
                            pass
                    
                    print(f"   🎉 找到可用的上传API!")
                    return True
                    
                except CloudreveError as e:
                    print(f"   ❌ 失败: {e}")
        
        # 测试其他HTTP方法
        print(f"\n📤 测试其他HTTP方法...")
        
        methods = ['POST', 'PATCH']
        for method in methods:
            print(f"\n🔍 测试方法: {method} /api/v4/file/upload")
            
            try:
                response = client._make_request(method, '/api/v4/file/upload', json=test_data_formats[0])
                print(f"   ✅ 成功! 响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except CloudreveError as e:
                print(f"   ❌ 失败: {e}")
        
        # 测试简单文件创建API
        print(f"\n📝 测试简单文件创建...")
        
        simple_endpoints = [
            '/api/v4/file',
            '/api/v4/files',
            '/api/v4/file/create'
        ]
        
        simple_data = {
            'name': 'test.mp4',
            'type': 'file',
            'content': 'test content'
        }
        
        for endpoint in simple_endpoints:
            print(f"\n🔍 测试端点: {endpoint}")
            
            try:
                response = client._make_request('POST', endpoint, json=simple_data)
                print(f"   ✅ 成功! 响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except CloudreveError as e:
                print(f"   ❌ 失败: {e}")
        
        # 登出
        client.logout()
        print(f"\n✅ 探索完成")
        
    except CloudreveError as e:
        print(f"❌ 探索失败: {e}")


if __name__ == "__main__":
    explore_upload_apis()
