#!/usr/bin/env python3
"""
Cloudreve V4 API客户端
基于官方API文档实现: https://cloudrevev4.apifox.cn
"""

import json
import time
import logging
import requests
from typing import Optional, Dict, Any, Tuple
from pathlib import Path


class CloudreveError(Exception):
    """Cloudreve API异常"""
    def __init__(self, message: str, code: int = None, response: Dict = None):
        super().__init__(message)
        self.code = code
        self.response = response


class CloudreveClient:
    """Cloudreve V4 API客户端"""
    
    def __init__(self, base_url: str, email: str, password: str, timeout: int = 60):
        """
        初始化Cloudreve客户端
        
        Args:
            base_url: Cloudreve服务器地址，如 "http://your-server.com:5212"
            email: 用户邮箱地址
            password: 用户密码
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.email = email
        self.password = password
        self.timeout = timeout
        
        # 认证信息
        self.access_token = None
        self.refresh_token = None
        self.token_expires = None
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        # 日志
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            API响应的JSON数据
            
        Raises:
            CloudreveError: API请求失败
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                raise CloudreveError(
                    f"HTTP {response.status_code}: {response.text}",
                    code=response.status_code
                )
            
            # 解析JSON响应
            try:
                data = response.json()
            except json.JSONDecodeError:
                raise CloudreveError(f"无效的JSON响应: {response.text}")
            
            # 检查API响应码
            if data.get('code', 0) != 0:
                raise CloudreveError(
                    data.get('msg', '未知错误'),
                    code=data.get('code'),
                    response=data
                )
            
            return data
            
        except requests.RequestException as e:
            raise CloudreveError(f"请求失败: {e}")
    
    def ping(self) -> str:
        """
        获取服务器版本信息
        
        Returns:
            服务器版本号
        """
        response = self._make_request('GET', '/api/v4/site/ping')
        return response['data']
    
    def get_captcha(self) -> Tuple[str, str]:
        """
        获取验证码
        
        Returns:
            (验证码图片base64, 验证码ticket)
        """
        response = self._make_request('GET', '/api/v4/site/captcha')
        data = response['data']
        return data['image'], data['ticket']
    
    def login(self, captcha: str = None, ticket: str = None) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            captcha: 验证码（如果需要）
            ticket: 验证码ticket（如果需要）
            
        Returns:
            登录响应数据，包含用户信息和token
            
        Raises:
            CloudreveError: 登录失败
        """
        login_data = {
            'email': self.email,
            'password': self.password
        }
        
        # 添加验证码（如果提供）
        if captcha and ticket:
            login_data['captcha'] = captcha
            login_data['ticket'] = ticket
        
        try:
            response = self._make_request('POST', '/api/v4/session/token', json=login_data)
            
            # 保存认证信息
            token_data = response['data']['token']
            self.access_token = token_data['access_token']
            self.refresh_token = token_data['refresh_token']
            
            # 解析token过期时间
            access_expires = token_data.get('access_expires')
            if access_expires:
                # 假设时间格式为ISO格式，需要解析
                # 这里简化处理，实际应该解析时间字符串
                self.token_expires = time.time() + 3600  # 默认1小时后过期
            
            # 设置认证头
            self.session.headers['Authorization'] = f'Bearer {self.access_token}'
            
            self.logger.info(f"登录成功: {response['data']['user']['email']}")
            return response['data']
            
        except CloudreveError as e:
            if e.code == 40001:
                raise CloudreveError("邮箱格式错误，请检查email地址", e.code, e.response)
            elif e.code == 40002:
                raise CloudreveError("密码错误", e.code, e.response)
            elif e.code == 40003:
                raise CloudreveError("需要验证码", e.code, e.response)
            else:
                raise
    
    def refresh_access_token(self) -> bool:
        """
        刷新访问令牌
        
        Returns:
            是否刷新成功
        """
        if not self.refresh_token:
            return False
        
        try:
            response = self._make_request(
                'POST', 
                '/api/v4/session/refresh',
                json={'refresh_token': self.refresh_token}
            )
            
            # 更新token
            token_data = response['data']
            self.access_token = token_data['access_token']
            self.session.headers['Authorization'] = f'Bearer {self.access_token}'
            
            self.logger.info("Token刷新成功")
            return True
            
        except CloudreveError:
            self.logger.warning("Token刷新失败")
            return False
    
    def ensure_authenticated(self) -> bool:
        """
        确保已认证，如果token过期则尝试刷新或重新登录
        
        Returns:
            是否认证成功
        """
        # 检查是否有token
        if not self.access_token:
            try:
                self.login()
                return True
            except CloudreveError:
                return False
        
        # 检查token是否过期
        if self.token_expires and time.time() > self.token_expires:
            # 尝试刷新token
            if not self.refresh_access_token():
                # 刷新失败，重新登录
                try:
                    self.login()
                    return True
                except CloudreveError:
                    return False
        
        return True
    
    def logout(self) -> bool:
        """
        用户登出
        
        Returns:
            是否登出成功
        """
        if not self.access_token:
            return True
        
        try:
            self._make_request('DELETE', '/api/v4/session/token')
            
            # 清除认证信息
            self.access_token = None
            self.refresh_token = None
            self.token_expires = None
            
            # 移除认证头
            if 'Authorization' in self.session.headers:
                del self.session.headers['Authorization']
            
            self.logger.info("登出成功")
            return True
            
        except CloudreveError:
            self.logger.warning("登出失败")
            return False

    def _create_upload_session(self, file_path: str, policy_id: str = None) -> Dict[str, Any]:
        """
        创建文件上传会话

        Args:
            file_path: 本地文件路径
            policy_id: 存储策略ID（可选）

        Returns:
            上传会话信息

        Raises:
            CloudreveError: 创建上传会话失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        file_path = Path(file_path)
        if not file_path.exists():
            raise CloudreveError(f"文件不存在: {file_path}")

        # 获取文件信息
        file_size = file_path.stat().st_size
        file_name = file_path.name

        # 构建上传URI，根据官方文档格式
        upload_uri = f"cloudreve://my/{file_name}"

        upload_data = {
            'uri': upload_uri,
            'size': file_size,
            'last_modified': int(file_path.stat().st_mtime * 1000),  # 毫秒时间戳
        }

        # 如果没有提供policy_id，尝试从根目录获取存储策略
        if not policy_id:
            try:
                # 通过get_storage_policies获取可用的存储策略
                policies = self.get_storage_policies()
                if policies and len(policies) > 0:
                    policy_id = policies[0]['id']
                    self.logger.info(f"自动获取到存储策略ID: {policy_id}")
                else:
                    raise CloudreveError("未找到可用的存储策略")
            except CloudreveError as e:
                self.logger.warning(f"自动获取存储策略失败: {e}")

                # 如果自动获取失败，尝试一些常见的策略ID
                common_policy_ids = ["1", "default", "local", "0"]

                for test_id in common_policy_ids:
                    test_data = upload_data.copy()
                    test_data['policy_id'] = test_id

                    try:
                        response = self._make_request('PUT', '/api/v4/file/upload', json=test_data)
                        self.logger.info(f"找到可用的存储策略: {test_id}")
                        return response['data']
                    except CloudreveError as e:
                        self.logger.debug(f"策略ID {test_id} 失败: {e}")
                        continue

                # 如果所有尝试都失败，抛出异常
                raise CloudreveError("无法找到可用的存储策略ID，请检查服务器配置或手动指定policy_id")

        upload_data['policy_id'] = policy_id

        # 尝试检测MIME类型
        mime_type = self._get_mime_type(file_path)
        if mime_type:
            upload_data['mime_type'] = mime_type

        response = self._make_request('PUT', '/api/v4/file/upload', json=upload_data)
        return response['data']

    def _upload_file_chunk(self, session_id: str, chunk_data: bytes, chunk_index: int) -> bool:
        """
        上传文件块

        Args:
            session_id: 上传会话ID
            chunk_data: 文件块数据
            chunk_index: 文件块索引

        Returns:
            是否上传成功
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        try:
            # 根据API文档，使用正确的端点格式: /file/upload/{sessionId}/{index}
            # 发送原始二进制数据，不使用multipart/form-data
            headers = {
                'Authorization': self.session.headers.get('Authorization'),
                'Content-Length': str(len(chunk_data)),
                'Content-Type': 'application/octet-stream'
            }

            response = self.session.post(
                f"{self.base_url}/api/v4/file/upload/{session_id}/{chunk_index}",
                data=chunk_data,
                headers=headers,
                timeout=self.timeout
            )

            if response.status_code != 200:
                raise CloudreveError(f"上传文件块失败: HTTP {response.status_code}")

            # 检查响应内容
            try:
                result = response.json()
                if result.get('code', 0) != 0:
                    raise CloudreveError(f"上传文件块失败: {result.get('msg', '未知错误')}")
            except ValueError:
                # 如果响应不是JSON格式，只要状态码是200就认为成功
                pass

            return True

        except requests.RequestException as e:
            raise CloudreveError(f"上传文件块失败: {e}")

    def upload_file(self, file_path: str, policy_id: str = None,
                   chunk_size: int = 5242880, progress_callback=None) -> str:
        """
        上传文件

        Args:
            file_path: 本地文件路径
            policy_id: 存储策略ID（可选）
            chunk_size: 文件块大小（默认5MB）
            progress_callback: 进度回调函数 callback(uploaded_bytes, total_bytes)

        Returns:
            上传后的文件URL或ID

        Raises:
            CloudreveError: 上传失败
        """
        file_path = Path(file_path)

        # 创建上传会话
        session_info = self._create_upload_session(str(file_path), policy_id)
        session_id = session_info['session_id']

        self.logger.info(f"开始上传文件: {file_path.name}")

        # 分块上传文件
        uploaded_bytes = 0
        total_bytes = file_path.stat().st_size
        chunk_index = 0

        with open(file_path, 'rb') as f:
            while True:
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break

                # 上传文件块
                self._upload_file_chunk(session_id, chunk_data, chunk_index)

                uploaded_bytes += len(chunk_data)
                chunk_index += 1

                # 调用进度回调
                if progress_callback:
                    progress_callback(uploaded_bytes, total_bytes)

                self.logger.debug(f"已上传 {uploaded_bytes}/{total_bytes} 字节")

        self.logger.info(f"文件上传完成: {file_path.name}")

        # 返回会话信息中的URI或其他标识
        return session_info.get('uri', session_id)

    def _get_mime_type(self, file_path: Path) -> Optional[str]:
        """
        获取文件MIME类型

        Args:
            file_path: 文件路径

        Returns:
            MIME类型字符串
        """
        import mimetypes
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type

    def list_files(self, path: str = "/", page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        列出文件和文件夹

        Args:
            path: 目录路径，默认为根目录
            page: 页码，从1开始
            page_size: 每页文件数量

        Returns:
            文件列表信息

        Raises:
            CloudreveError: 获取文件列表失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        params = {
            'path': path,
            'page': page,
            'page_size': page_size
        }

        response = self._make_request('GET', '/api/v4/file/list', params=params)
        return response['data']

    def get_file_info(self, file_id: str) -> Dict[str, Any]:
        """
        获取文件详细信息

        Args:
            file_id: 文件ID

        Returns:
            文件详细信息

        Raises:
            CloudreveError: 获取文件信息失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        response = self._make_request('GET', f'/api/v4/file/info/{file_id}')
        return response['data']

    def delete_file(self, file_ids: list, force: bool = False) -> bool:
        """
        删除文件或文件夹

        Args:
            file_ids: 文件ID列表
            force: 是否强制删除（跳过回收站）

        Returns:
            是否删除成功

        Raises:
            CloudreveError: 删除失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        if not isinstance(file_ids, list):
            file_ids = [file_ids]

        delete_data = {
            'items': file_ids,
            'force': force
        }

        response = self._make_request('DELETE', '/api/v4/file/delete', json=delete_data)

        self.logger.info(f"删除文件成功: {len(file_ids)} 个文件")
        return True

    def create_download_url(self, file_id: str) -> str:
        """
        创建文件下载链接

        Args:
            file_id: 文件ID

        Returns:
            下载链接URL

        Raises:
            CloudreveError: 创建下载链接失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        download_data = {
            'items': [file_id]
        }

        response = self._make_request('POST', '/api/v4/file/download', json=download_data)
        return response['data']['url']

    def create_share_link(self, file_uri: str, password: str = None,
                         expire_days: int = None, is_private: bool = None,
                         preview_enabled: bool = True) -> Dict[str, Any]:
        """
        创建分享链接（基于Cloudreve V4 API）

        Args:
            file_uri: 文件URI，格式如 "cloudreve://my/filename.txt"
            password: 分享密码（可选）
            expire_days: 过期天数（可选，0表示永不过期）
            is_private: 是否为私有分享（需要密码）
            preview_enabled: 是否允许预览

        Returns:
            分享链接信息

        Raises:
            CloudreveError: 创建分享链接失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        # 根据Cloudreve V4 API文档构建请求数据
        share_data = {
            'uri': file_uri,
            'permissions': {
                'anonymous': 'BQ==',  # 基本权限，允许下载
                'everyone': 'AQ=='   # 基本权限
            }
        }

        # 添加可选参数
        if is_private is not None:
            share_data['is_private'] = is_private
        elif password:
            share_data['is_private'] = True  # 如果设置了密码，自动设为私有

        if password:
            share_data['password'] = password

        if preview_enabled is not None:
            share_data['share_view'] = preview_enabled

        if expire_days is not None:
            if expire_days == 0:
                share_data['expire'] = None  # 永不过期
            else:
                # 计算过期秒数
                expire_seconds = expire_days * 24 * 3600
                share_data['expire'] = expire_seconds

        # 使用正确的API端点和方法
        response = self._make_request('PUT', '/api/v4/share', json=share_data)

        share_url = response['data']
        self.logger.info(f"创建分享链接成功: {share_url}")
        return {'url': share_url}

    def list_share_links(self, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        获取我的分享链接列表

        Args:
            page: 页码，从1开始
            page_size: 每页数量

        Returns:
            分享链接列表

        Raises:
            CloudreveError: 获取分享链接失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        params = {
            'page': page,
            'page_size': page_size
        }

        response = self._make_request('GET', '/api/v4/share/list', params=params)
        return response['data']

    def delete_share_link(self, share_id: str) -> bool:
        """
        删除分享链接

        Args:
            share_id: 分享链接ID

        Returns:
            是否删除成功

        Raises:
            CloudreveError: 删除分享链接失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        response = self._make_request('DELETE', f'/api/v4/share/{share_id}')

        self.logger.info(f"删除分享链接成功: {share_id}")
        return True

    def get_share_info(self, share_key: str, password: str = None) -> Dict[str, Any]:
        """
        获取分享链接信息

        Args:
            share_key: 分享链接key
            password: 分享密码（如果需要）

        Returns:
            分享链接详细信息

        Raises:
            CloudreveError: 获取分享信息失败
        """
        share_data = {'key': share_key}

        if password:
            share_data['password'] = password

        response = self._make_request('POST', '/api/v4/share/info', json=share_data)
        return response['data']

    def rename_file(self, file_id: str, new_name: str) -> bool:
        """
        重命名文件或文件夹

        Args:
            file_id: 文件ID
            new_name: 新名称

        Returns:
            是否重命名成功

        Raises:
            CloudreveError: 重命名失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        rename_data = {
            'item': file_id,
            'new_name': new_name
        }

        response = self._make_request('POST', '/api/v4/file/rename', json=rename_data)

        self.logger.info(f"重命名文件成功: {file_id} -> {new_name}")
        return True

    def move_files(self, file_ids: list, target_path: str, copy: bool = False) -> bool:
        """
        移动或复制文件

        Args:
            file_ids: 文件ID列表
            target_path: 目标路径
            copy: 是否复制（False为移动）

        Returns:
            是否操作成功

        Raises:
            CloudreveError: 移动/复制失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        if not isinstance(file_ids, list):
            file_ids = [file_ids]

        move_data = {
            'items': file_ids,
            'target': target_path,
            'copy': copy
        }

        response = self._make_request('POST', '/api/v4/file/move', json=move_data)

        action = "复制" if copy else "移动"
        self.logger.info(f"{action}文件成功: {len(file_ids)} 个文件到 {target_path}")
        return True

    def create_folder(self, folder_name: str, parent_path: str = "/") -> Dict[str, Any]:
        """
        创建文件夹

        Args:
            folder_name: 文件夹名称
            parent_path: 父目录路径

        Returns:
            创建的文件夹信息

        Raises:
            CloudreveError: 创建文件夹失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        # 构建完整路径
        if parent_path.endswith('/'):
            full_path = f"{parent_path}{folder_name}"
        else:
            full_path = f"{parent_path}/{folder_name}"

        folder_data = {
            'path': full_path
        }

        response = self._make_request('POST', '/api/v4/file/mkdir', json=folder_data)

        self.logger.info(f"创建文件夹成功: {full_path}")
        return response['data']

    def search_files(self, keyword: str, path: str = "/",
                    file_type: str = None, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        搜索文件

        Args:
            keyword: 搜索关键词
            path: 搜索路径
            file_type: 文件类型过滤（如 'image', 'video', 'audio', 'document'）
            page: 页码
            page_size: 每页数量

        Returns:
            搜索结果

        Raises:
            CloudreveError: 搜索失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        params = {
            'q': keyword,
            'path': path,
            'page': page,
            'page_size': page_size
        }

        if file_type:
            params['type'] = file_type

        response = self._make_request('GET', '/api/v4/file/search', params=params)
        return response['data']

    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储空间信息

        Returns:
            存储空间使用情况

        Raises:
            CloudreveError: 获取存储信息失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        response = self._make_request('GET', '/api/v4/user/storage')
        return response['data']

    def get_storage_policies(self) -> list:
        """
        通过查询根目录获取可用的存储策略信息

        根据Cloudreve API文档，存储策略信息包含在/file端点的响应中

        Returns:
            存储策略列表，如果只有一个策略则返回包含该策略的列表

        Raises:
            CloudreveError: 获取存储策略失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        # 查询根目录以获取存储策略信息
        params = {
            'uri': 'cloudreve://my/',
            'page': 0,
            'page_size': 1  # 只需要获取策略信息，不需要文件列表
        }

        response = self._make_request('GET', '/api/v4/file', params=params)

        if 'data' not in response:
            raise CloudreveError("响应中缺少data字段")

        data = response['data']

        # 检查是否包含存储策略信息
        if 'storage_policy' not in data or not data['storage_policy']:
            raise CloudreveError("未找到存储策略信息")

        storage_policy = data['storage_policy']

        # 返回策略列表格式，保持与原API的兼容性
        return [storage_policy]
