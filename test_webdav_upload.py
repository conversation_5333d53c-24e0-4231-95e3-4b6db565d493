#!/usr/bin/env python3
"""测试WebDAV上传"""

import sys
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_webdav_upload():
    """测试WebDAV上传"""
    print("🌐 测试WebDAV上传")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    # 检查test.mp4文件
    test_file = Path("test.mp4")
    if not test_file.exists():
        print("❌ test.mp4文件不存在")
        return False
    
    print(f"📁 文件信息:")
    print(f"   文件名: {test_file.name}")
    print(f"   文件大小: {test_file.stat().st_size} 字节")
    
    # 尝试WebDAV上传
    webdav_urls = [
        f"{base_url}/dav",
        f"{base_url}/webdav",
        f"{base_url}/api/v4/webdav",
        f"{base_url}/remote.php/webdav"  # 类似NextCloud的路径
    ]
    
    for webdav_url in webdav_urls:
        print(f"\n🔍 测试WebDAV端点: {webdav_url}")
        
        try:
            # 测试WebDAV连接
            response = requests.request(
                'PROPFIND',
                webdav_url,
                auth=(username, password),
                timeout=10
            )
            
            print(f"   PROPFIND状态: {response.status_code}")
            
            if response.status_code in [200, 207, 401]:  # 401表示需要认证，但端点存在
                print(f"   ✅ WebDAV端点可能可用")
                
                # 尝试上传文件
                upload_url = f"{webdav_url}/test.mp4"
                
                with open(test_file, 'rb') as f:
                    file_data = f.read()
                
                upload_response = requests.put(
                    upload_url,
                    data=file_data,
                    auth=(username, password),
                    headers={'Content-Type': 'video/mp4'},
                    timeout=60
                )
                
                print(f"   上传状态: {upload_response.status_code}")
                print(f"   响应: {upload_response.text[:200]}")
                
                if upload_response.status_code in [200, 201, 204]:
                    print(f"   🎉 WebDAV上传成功!")
                    
                    # 验证文件是否存在
                    check_response = requests.request(
                        'PROPFIND',
                        upload_url,
                        auth=(username, password),
                        timeout=10
                    )
                    
                    if check_response.status_code in [200, 207]:
                        print(f"   ✅ 文件验证成功，已上传到: {upload_url}")
                        return True
                    else:
                        print(f"   ⚠️ 文件验证失败: {check_response.status_code}")
                else:
                    print(f"   ❌ WebDAV上传失败")
            else:
                print(f"   ❌ WebDAV端点不可用")
                
        except Exception as e:
            print(f"   ❌ WebDAV测试失败: {e}")
    
    # 尝试直接HTTP上传
    print(f"\n📤 测试直接HTTP上传...")
    
    upload_endpoints = [
        f"{base_url}/upload",
        f"{base_url}/api/upload",
        f"{base_url}/api/v4/upload/direct"
    ]
    
    for endpoint in upload_endpoints:
        print(f"\n🔍 测试端点: {endpoint}")
        
        try:
            # 尝试multipart/form-data上传
            with open(test_file, 'rb') as f:
                files = {'file': (test_file.name, f, 'video/mp4')}
                data = {'path': '/'}
                
                response = requests.post(
                    endpoint,
                    files=files,
                    data=data,
                    auth=(username, password),
                    timeout=60
                )
                
                print(f"   状态: {response.status_code}")
                print(f"   响应: {response.text[:200]}")
                
                if response.status_code in [200, 201]:
                    print(f"   🎉 HTTP上传成功!")
                    return True
                    
        except Exception as e:
            print(f"   ❌ HTTP上传失败: {e}")
    
    print(f"\n❌ 所有上传方法都失败了")
    return False


if __name__ == "__main__":
    test_webdav_upload()
