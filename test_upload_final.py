#!/usr/bin/env python3
"""最终的上传功能测试"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_upload_final():
    """最终的上传功能测试"""
    print("🔍 最终的上传功能测试")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 测试获取存储策略
        print(f"\n📋 获取存储策略...")
        try:
            policies = client.get_storage_policies()
            print(f"✅ 成功获取存储策略!")
            
            for i, policy in enumerate(policies, 1):
                policy_id = policy.get('id', 'N/A')
                policy_name = policy.get('name', 'N/A')
                policy_type = policy.get('type', 'N/A')
                print(f"   策略 {i}: ID={policy_id}, 名称={policy_name}, 类型={policy_type}")
                
        except CloudreveError as e:
            print(f"❌ 获取存储策略失败: {e}")
            return
        
        # 创建唯一的测试文件名（使用时间戳）
        timestamp = int(time.time())
        test_file = Path(f"test_upload_{timestamp}.txt")
        test_content = f"""这是Cloudreve上传功能的最终测试文件
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
时间戳: {timestamp}
文件大小: 这行文字用于增加文件大小，确保测试上传功能的完整性。
"""
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            print(f"\n📁 创建测试文件:")
            print(f"   文件名: {test_file.name}")
            print(f"   文件大小: {len(test_content.encode('utf-8'))} 字节")
            
            # 测试完整的文件上传流程
            print(f"\n🚀 开始文件上传...")
            
            def progress_callback(uploaded, total):
                percent = (uploaded / total) * 100
                print(f"   📊 上传进度: {percent:.1f}% ({uploaded}/{total} 字节)")
            
            # 使用upload_file方法进行完整上传
            result = client.upload_file(
                str(test_file), 
                progress_callback=progress_callback
            )
            
            print(f"✅ 文件上传完成!")
            print(f"📁 上传结果: {result}")
            
            # 验证文件上传
            print(f"\n📋 验证文件上传...")
            try:
                # 查询根目录文件列表
                params = {
                    'uri': 'cloudreve://my/',
                    'page': 0,
                    'page_size': 50
                }
                response = client._make_request('GET', '/api/v4/file', params=params)
                
                if 'data' in response and 'files' in response['data']:
                    files = response['data']['files']
                    uploaded_file = None
                    
                    for file_info in files:
                        if file_info.get('name') == test_file.name:
                            uploaded_file = file_info
                            break
                    
                    if uploaded_file:
                        print(f"✅ 成功找到上传的文件:")
                        print(f"   文件名: {uploaded_file.get('name')}")
                        print(f"   文件ID: {uploaded_file.get('id')}")
                        print(f"   文件大小: {uploaded_file.get('size')} 字节")
                        print(f"   创建时间: {uploaded_file.get('created_at')}")
                        print(f"   文件路径: {uploaded_file.get('path')}")
                        
                        # 测试创建分享链接
                        print(f"\n🔗 测试创建分享链接...")
                        try:
                            share_info = client.create_share_link(
                                file_ids=[uploaded_file.get('id')],
                                expire_days=7,
                                preview_enabled=True
                            )
                            
                            share_key = share_info.get('key', '')
                            share_url = f"{base_url}/s/{share_key}"
                            
                            print(f"✅ 分享链接创建成功:")
                            print(f"   🌐 分享链接: {share_url}")
                            print(f"   🔑 分享密钥: {share_key}")
                            print(f"   ⏰ 有效期: 7天")
                            print(f"   👁️ 允许预览: 是")
                            
                        except CloudreveError as e:
                            print(f"⚠️ 创建分享链接失败: {e}")
                        
                    else:
                        print(f"⚠️ 未在文件列表中找到上传的文件")
                        print(f"   可能的原因:")
                        print(f"   - 文件索引需要时间更新")
                        print(f"   - 文件被上传到了其他目录")
                        print(f"   - 权限问题")
                
            except CloudreveError as e:
                print(f"⚠️ 验证文件上传失败: {e}")
            
        except CloudreveError as e:
            print(f"❌ 文件上传失败: {e}")
            if e.response:
                print(f"   详细错误: {e.response}")
        
        except Exception as e:
            print(f"❌ 文件上传异常: {e}")
        
        finally:
            # 清理本地测试文件
            if test_file.exists():
                test_file.unlink()
                print(f"\n🗑️ 清理本地测试文件: {test_file.name}")
        
        # 登出
        client.logout()
        print(f"\n✅ 测试完成")
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")
        if e.response:
            print(f"   详细错误: {e.response}")


if __name__ == "__main__":
    test_upload_final()
