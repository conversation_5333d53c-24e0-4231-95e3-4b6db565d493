#!/usr/bin/env python3
"""测试修复后的存储策略获取功能"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_fixed_policies():
    """测试修复后的存储策略获取功能"""
    print("🔍 测试修复后的存储策略获取功能")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 测试获取存储策略
        print(f"\n📋 测试获取存储策略...")
        try:
            policies = client.get_storage_policies()
            print(f"✅ 成功获取存储策略!")
            print(f"📋 找到 {len(policies)} 个存储策略:")
            
            for i, policy in enumerate(policies, 1):
                policy_id = policy.get('id', 'N/A')
                policy_name = policy.get('name', 'N/A')
                policy_type = policy.get('type', 'N/A')
                max_size = policy.get('max_size', 0)
                print(f"   {i}. ID: {policy_id}")
                print(f"      名称: {policy_name}")
                print(f"      类型: {policy_type}")
                print(f"      最大文件大小: {max_size} 字节 ({'无限制' if max_size == 0 else f'{max_size / 1024 / 1024:.1f} MB'})")
                
        except CloudreveError as e:
            print(f"❌ 获取存储策略失败: {e}")
            return
        
        # 测试文件上传
        print(f"\n📤 测试文件上传...")
        
        # 创建测试文件
        test_file = Path("test_upload_fixed.txt")
        test_content = "这是测试修复后上传功能的文件\n测试时间: 2025-06-30"
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            print(f"📁 创建测试文件: {test_file.name} ({len(test_content)} 字节)")
            
            # 测试创建上传会话
            print(f"\n🔄 测试创建上传会话...")
            try:
                session_info = client.create_upload_session(str(test_file))
                print(f"✅ 上传会话创建成功!")
                print(f"📋 会话信息:")
                print(f"   会话ID: {session_info.get('session_id', 'N/A')}")
                print(f"   上传ID: {session_info.get('upload_id', 'N/A')}")
                print(f"   块大小: {session_info.get('chunk_size', 'N/A')} 字节")
                print(f"   过期时间: {session_info.get('expires', 'N/A')}")
                
                if 'storage_policy' in session_info:
                    policy = session_info['storage_policy']
                    print(f"   使用的存储策略:")
                    print(f"     ID: {policy.get('id')}")
                    print(f"     名称: {policy.get('name')}")
                    print(f"     类型: {policy.get('type')}")
                
                # 如果会话创建成功，尝试完整上传
                print(f"\n🚀 开始完整文件上传...")
                
                def progress_callback(uploaded, total):
                    percent = (uploaded / total) * 100
                    print(f"   📊 上传进度: {percent:.1f}% ({uploaded}/{total} 字节)")
                
                result = client.upload_file(str(test_file), progress_callback=progress_callback)
                print(f"✅ 文件上传完成!")
                print(f"📁 上传结果: {result}")
                
                # 测试文件列表，看看是否能找到上传的文件
                print(f"\n📋 验证文件上传...")
                try:
                    # 查询根目录文件列表
                    params = {
                        'uri': 'cloudreve://my/',
                        'page': 0,
                        'page_size': 50
                    }
                    response = client._make_request('GET', '/api/v4/file', params=params)
                    
                    if 'data' in response and 'files' in response['data']:
                        files = response['data']['files']
                        uploaded_file = None
                        
                        for file_info in files:
                            if file_info.get('name') == test_file.name:
                                uploaded_file = file_info
                                break
                        
                        if uploaded_file:
                            print(f"✅ 找到上传的文件:")
                            print(f"   文件名: {uploaded_file.get('name')}")
                            print(f"   文件ID: {uploaded_file.get('id')}")
                            print(f"   文件大小: {uploaded_file.get('size')} 字节")
                            print(f"   创建时间: {uploaded_file.get('created_at')}")
                        else:
                            print(f"⚠️ 未在文件列表中找到上传的文件，可能需要等待索引更新")
                    
                except CloudreveError as e:
                    print(f"⚠️ 验证文件上传失败: {e}")
                
            except CloudreveError as e:
                print(f"❌ 上传失败: {e}")
                if e.response:
                    print(f"   详细错误: {e.response}")
        
        except Exception as e:
            print(f"❌ 文件上传测试异常: {e}")
        
        finally:
            # 清理测试文件
            if test_file.exists():
                test_file.unlink()
                print(f"\n🗑️ 清理本地测试文件")
        
        # 登出
        client.logout()
        print(f"\n✅ 测试完成")
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_fixed_policies()
