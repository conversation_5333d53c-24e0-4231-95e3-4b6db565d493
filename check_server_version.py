#!/usr/bin/env python3
"""检查服务器版本和可用功能"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def check_server():
    """检查服务器版本和功能"""
    print("🔍 检查Cloudreve服务器版本和功能")
    print("=" * 50)
    
    # 直接从项目根目录的config.toml读取配置
    try:
        # 手动解析TOML配置文件
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单解析Cloudreve配置
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                # 移除行内注释
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    try:
        # 创建客户端
        client = CloudreveClient(base_url, username, password)
        
        print(f"🌐 服务器地址: {base_url}")
        
        # 1. 检查服务器版本（不需要认证）
        print(f"\n📋 检查服务器版本...")
        try:
            version = client.ping()
            print(f"   ✅ 服务器版本: {version}")
        except CloudreveError as e:
            print(f"   ❌ 获取版本失败: {e}")
        
        # 2. 登录
        print(f"\n🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 3. 检查用户设置（已知可用）
        print(f"\n⚙️ 检查用户设置...")
        try:
            settings = client._make_request('GET', '/api/v4/user/setting')
            print(f"   ✅ 用户设置: {settings}")
        except CloudreveError as e:
            print(f"   ❌ 获取用户设置失败: {e}")
        
        # 4. 尝试直接上传文件来测试上传功能
        print(f"\n📤 测试文件上传功能...")
        
        # 创建一个小测试文件
        test_file = Path("test_upload.txt")
        test_content = "这是一个测试文件，用于测试Cloudreve上传功能。"
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            print(f"   📁 创建测试文件: {test_file.name} ({len(test_content)} 字节)")
            
            # 尝试创建上传会话，不指定policy_id让系统自动选择
            print(f"   🔄 尝试创建上传会话...")
            
            # 构建上传数据
            file_size = test_file.stat().st_size
            upload_data = {
                'uri': f"cloudreve://my/{test_file.name}",
                'size': file_size,
                'last_modified': int(test_file.stat().st_mtime * 1000),
                'mime_type': 'text/plain'
            }
            
            # 尝试不同的策略ID
            policy_ids_to_try = [None, "1", "0", "default", "local"]
            
            for policy_id in policy_ids_to_try:
                test_data = upload_data.copy()
                if policy_id:
                    test_data['policy_id'] = policy_id
                    print(f"   🔍 尝试策略ID: {policy_id}")
                else:
                    print(f"   🔍 尝试不指定策略ID")
                
                try:
                    response = client._make_request('PUT', '/api/v4/file/upload', json=test_data)
                    print(f"   ✅ 上传会话创建成功!")
                    print(f"   📋 会话信息: {response}")
                    break
                except CloudreveError as e:
                    print(f"   ❌ 失败: {e}")
                    if e.response:
                        print(f"   详细错误: {e.response}")
            
            # 清理测试文件
            if test_file.exists():
                test_file.unlink()
                print(f"   🗑️ 清理测试文件")
                
        except Exception as e:
            print(f"   ❌ 测试上传功能失败: {e}")
        
        # 5. 检查其他可能的管理员API
        print(f"\n🔧 检查管理员功能...")
        admin_endpoints = [
            '/api/v4/admin/setting',
            '/api/v4/admin/policies',
            '/api/v4/admin/storage',
        ]
        
        for endpoint in admin_endpoints:
            try:
                response = client._make_request('GET', endpoint)
                print(f"   ✅ {endpoint}: {response}")
            except CloudreveError as e:
                print(f"   ❌ {endpoint}: {e}")
        
        # 登出
        client.logout()
        print(f"\n✅ 检查完成")
        
    except CloudreveError as e:
        print(f"❌ 检查失败: {e}")


if __name__ == "__main__":
    check_server()
