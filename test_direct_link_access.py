#!/usr/bin/env python3
"""测试直链访问，使用正确的服务器地址"""

import sys
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_direct_link_access():
    """测试直链访问，使用正确的服务器地址"""
    print("🔍 测试直链访问")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    print(f"🌐 服务器地址: {base_url}")
    
    # 使用之前创建的直链进行测试
    test_direct_links = [
        "http://localhost:5212/f/1mHZ/test_direct_link_1751345668.txt",
        "http://*************:32203/f/1mHZ/test_direct_link_1751345668.txt"  # 修正的地址
    ]
    
    for direct_link in test_direct_links:
        print(f"\n🔗 测试直链: {direct_link}")
        
        try:
            response = requests.get(direct_link, timeout=10)
            print(f"   HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_received = response.text
                print(f"   ✅ 直链访问成功!")
                print(f"   📄 接收到的内容长度: {len(content_received)} 字符")
                print(f"   📄 内容预览: {content_received[:100]}...")
            else:
                print(f"   ❌ 直链访问失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text[:200]}")
                
        except requests.RequestException as e:
            print(f"   ❌ 直链访问异常: {e}")
    
    # 测试创建新的直链，看看是否能获得正确的URL
    try:
        client = CloudreveClient(base_url, username, password)
        
        print(f"\n🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 查找现有文件
        print(f"\n📋 查找现有文件...")
        params = {
            'uri': 'cloudreve://my/',
            'page': 0,
            'page_size': 10
        }
        response = client._make_request('GET', '/api/v4/file', params=params)
        
        if 'data' in response and 'files' in response['data']:
            files = response['data']['files']
            
            if files:
                # 使用第一个文件测试直链
                test_file = files[0]
                file_name = test_file.get('name')
                file_path = test_file.get('path')
                
                print(f"   📁 测试文件: {file_name}")
                print(f"   📁 文件路径: {file_path}")
                
                # 构建文件URI
                file_uri = f"cloudreve://my/{file_name}"
                
                print(f"\n🔗 为现有文件创建直链...")
                try:
                    direct_link = client.create_direct_link(file_uri)
                    print(f"✅ 直链创建成功: {direct_link}")
                    
                    # 修正直链URL中的地址
                    if "localhost:5212" in direct_link:
                        corrected_link = direct_link.replace("localhost:5212", "*************:32203")
                        print(f"🔧 修正后的直链: {corrected_link}")
                        
                        # 测试修正后的直链
                        print(f"\n🌐 测试修正后的直链...")
                        try:
                            response = requests.get(corrected_link, timeout=10)
                            print(f"   HTTP状态码: {response.status_code}")
                            
                            if response.status_code == 200:
                                content_received = response.text
                                print(f"   ✅ 修正后的直链访问成功!")
                                print(f"   📄 接收到的内容长度: {len(content_received)} 字符")
                                print(f"   📄 内容预览: {content_received[:100]}...")
                            else:
                                print(f"   ❌ 修正后的直链访问失败: HTTP {response.status_code}")
                                print(f"   响应内容: {response.text[:200]}")
                                
                        except requests.RequestException as e:
                            print(f"   ❌ 修正后的直链访问异常: {e}")
                    
                except CloudreveError as e:
                    print(f"❌ 创建直链失败: {e}")
            else:
                print(f"   ⚠️ 未找到任何文件")
        
        client.logout()
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")
    
    print(f"\n✅ 测试完成")


if __name__ == "__main__":
    test_direct_link_access()
