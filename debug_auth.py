#!/usr/bin/env python3
"""调试认证问题"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def debug_auth():
    """调试认证问题"""
    print("🔍 调试Cloudreve认证问题")
    print("=" * 50)
    
    # 直接从项目根目录的config.toml读取配置
    try:
        # 手动解析TOML配置文件
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单解析Cloudreve配置
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                # 移除行内注释
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    try:
        # 创建客户端
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        print(f"   服务器: {base_url}")
        print(f"   用户: {username}")
        
        # 登录
        login_result = client.login()
        print(f"✅ 登录成功")
        
        # 检查登录响应结构
        print(f"\n📋 登录响应结构:")
        print(f"   完整响应: {login_result}")
        
        # 检查token信息
        if 'token' in login_result:
            token_info = login_result['token']
            print(f"\n🔑 Token信息:")
            print(f"   access_token: {token_info.get('access_token', 'N/A')[:50]}...")
            print(f"   refresh_token: {token_info.get('refresh_token', 'N/A')[:50]}...")
            print(f"   expires: {token_info.get('access_expires', 'N/A')}")
        
        # 检查客户端状态
        print(f"\n🔧 客户端状态:")
        print(f"   access_token: {client.access_token[:50] if client.access_token else 'None'}...")
        print(f"   refresh_token: {client.refresh_token[:50] if client.refresh_token else 'None'}...")
        
        # 检查session headers
        print(f"\n📡 Session Headers:")
        for key, value in client.session.headers.items():
            if key == 'Authorization':
                print(f"   {key}: {value[:50]}...")
            else:
                print(f"   {key}: {value}")
        
        # 测试获取存储策略
        print(f"\n🗄️ 测试获取存储策略...")
        try:
            # 直接调用API，查看详细错误
            response = client._make_request('GET', '/api/v4/user/setting/policies')
            print(f"   ✅ 成功获取存储策略: {response}")
            
            if 'data' in response and response['data']:
                policies = response['data']
                print(f"   📋 找到 {len(policies)} 个存储策略:")
                for i, policy in enumerate(policies, 1):
                    policy_id = policy.get('id', 'N/A')
                    policy_name = policy.get('name', 'N/A')
                    policy_type = policy.get('type', 'N/A')
                    print(f"      {i}. ID: {policy_id}, 名称: {policy_name}, 类型: {policy_type}")
            
        except CloudreveError as e:
            print(f"   ❌ 获取存储策略失败: {e}")
            print(f"   错误码: {e.code}")
            print(f"   响应: {e.response}")
        
        # 登出
        client.logout()
        print(f"\n✅ 调试完成")
        
    except CloudreveError as e:
        print(f"❌ 调试失败: {e}")
        print(f"   错误码: {e.code}")
        print(f"   响应: {e.response}")


if __name__ == "__main__":
    debug_auth()
