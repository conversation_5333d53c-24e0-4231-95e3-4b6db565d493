#!/usr/bin/env python3
"""测试获取存储策略"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_storage_policies():
    """测试获取存储策略"""
    print("🔍 测试获取存储策略")
    print("=" * 50)
    
    # 直接从项目根目录的config.toml读取配置
    try:
        # 手动解析TOML配置文件
        with open('config.toml', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单解析Cloudreve配置
        base_url = ''
        username = ''
        password = ''
        
        lines = content.split('\n')
        in_cloudreve_section = False
        
        for line in lines:
            line = line.strip()
            if line == '[file_uploader.uploaders.cloudreve]':
                in_cloudreve_section = True
                continue
            elif line.startswith('[') and in_cloudreve_section:
                in_cloudreve_section = False
                continue
            
            if in_cloudreve_section and '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                
                # 移除行内注释
                if '#' in value:
                    value = value.split('#')[0].strip().strip('"').strip("'")
                
                if key == 'base_url':
                    base_url = value
                elif key == 'username':
                    username = value
                elif key == 'password':
                    password = value
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔐 正在登录...")
        client.login()
        print(f"✅ 登录成功")
        
        # 测试各种可能的存储策略API端点
        endpoints = [
            '/api/v4/user/setting/policies',
            '/api/v4/user/policies',
            '/api/v4/policies',
            '/api/v4/storage/policies',
            '/api/v4/user/storage/policies'
        ]
        
        for endpoint in endpoints:
            print(f"\n🔍 测试端点: {endpoint}")
            try:
                response = client._make_request('GET', endpoint)
                print(f"   ✅ 成功! 响应: {response}")
                
                if 'data' in response:
                    policies = response['data']
                    if isinstance(policies, list):
                        print(f"   📋 找到 {len(policies)} 个存储策略:")
                        for i, policy in enumerate(policies, 1):
                            policy_id = policy.get('id', 'N/A')
                            policy_name = policy.get('name', 'N/A')
                            policy_type = policy.get('type', 'N/A')
                            print(f"      {i}. ID: {policy_id}, 名称: {policy_name}, 类型: {policy_type}")
                    else:
                        print(f"   📋 响应数据: {policies}")
                break
                
            except CloudreveError as e:
                print(f"   ❌ 失败: {e}")
        
        # 测试获取用户信息
        print(f"\n👤 测试获取用户信息...")
        try:
            user_response = client._make_request('GET', '/api/v4/user')
            print(f"   ✅ 用户信息: {user_response}")
        except CloudreveError as e:
            print(f"   ❌ 获取用户信息失败: {e}")
        
        # 测试获取存储信息
        print(f"\n💾 测试获取存储信息...")
        try:
            storage_response = client._make_request('GET', '/api/v4/user/storage')
            print(f"   ✅ 存储信息: {storage_response}")
        except CloudreveError as e:
            print(f"   ❌ 获取存储信息失败: {e}")
        
        # 登出
        client.logout()
        print(f"\n✅ 测试完成")
        
    except CloudreveError as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_storage_policies()
